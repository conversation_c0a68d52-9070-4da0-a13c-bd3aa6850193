# Knip Configuration Guide

Knip is configured for this Next.js project to help identify unused files, dependencies, and exports. This guide explains how to use knip effectively in your development workflow.

## What is Knip?

Knip is a comprehensive tool that finds:

- **Unused files** - Files that are not imported anywhere
- **Unused dependencies** - npm packages listed in package.json but not used
- **Unused exports** - Functions, types, and variables that are exported but never imported
- **Unlisted binaries** - Command-line tools used in scripts but not listed as dependencies

## Available Scripts

The following knip scripts are available in `package.json`:

```bash
# Run knip analysis (shows all issues)
pnpm knip

# Run knip in production mode (ignores devDependencies)
pnpm knip:production

# Run knip with auto-fix for some issues
pnpm knip:fix

# Run knip in watch mode (re-runs on file changes)
pnpm knip:watch
```

## Current Analysis Results

Based on the latest run, knip found the following issues:

### Unused Files (2)

- `src/lib/api/apiTester.ts` - Test file that's not being used
- `src/utils/csrc-fund-api.js` - JavaScript utility that may be legacy code

### Unused Dependencies (3)

- `date-fns` - Date manipulation library
- `radix-ui` - UI component library
- `react-hook-form` - Form handling library

### Unused DevDependencies (2)

- `eslint-config-next` - ESLint configuration for Next.js
- `eslint-import-resolver-typescript` - TypeScript import resolver for ESLint

### Unused Exports (16)

Various functions and types that are exported but not used elsewhere in the codebase.

## How to Address Issues

### 1. Unused Files

**Options:**

- Delete the files if they're truly unused
- Add them to knip's ignore list if they're needed for future use
- Import them in appropriate places if they should be used

### 2. Unused Dependencies

**Options:**

- Remove unused dependencies: `pnpm remove package-name`
- Keep them if you plan to use them soon
- Add them to `ignoreDependencies` in knip config if they're used indirectly

### 3. Unused Exports

**Options:**

- Remove unused exports from files
- Keep them if they're part of a public API
- Use them in appropriate places

### 4. Unlisted Binaries

**Options:**

- Add missing dependencies: `pnpm add -D knip`
- Update scripts to use listed binaries

## Configuration

The knip configuration is in `knip.config.ts`:

```typescript
// Entry points - files that are the starting points of your application
entry: [
  "src/app/**/{page,layout,loading,error,not-found,global-error}.{js,jsx,ts,tsx}",
  "src/app/**/route.{js,ts}",
  "src/app/globals.css",
  "postcss.config.{js,mjs}",
  "setup.sh",
];

// Project files to analyze
project: [
  "src/**/*.{js,jsx,ts,tsx}",
  "*.{js,jsx,ts,tsx,mjs}",
  "!**/*.{test,spec}.{js,jsx,ts,tsx}",
];

// Files/directories to ignore
ignore: [
  ".next/**",
  "node_modules/**",
  "coverage/**",
  "docs/**",
  "public/**",
  // ... more patterns
];
```

## Best Practices

### 1. Regular Cleanup

Run knip regularly (weekly/monthly) to keep your codebase clean:

```bash
pnpm knip
```

### 2. CI Integration

Add knip to your CI pipeline to catch unused code early:

```yaml
# In your GitHub Actions workflow
- name: Check for unused code
  run: pnpm knip
```

### 3. Gradual Cleanup

Don't try to fix everything at once. Focus on:

1. Unused dependencies first (easy wins)
2. Unused files second
3. Unused exports last (might be intentional APIs)

### 4. Team Workflow

- Review knip results before major releases
- Use knip reports to guide refactoring efforts
- Document intentionally unused exports

## Ignoring False Positives

If knip reports something as unused but it's actually needed:

### Ignore specific dependencies:

```typescript
ignoreDependencies: [
  "some-package", // Used indirectly
];
```

### Ignore specific files:

```typescript
ignore: [
  "src/lib/legacy/**", // Legacy code to keep
];
```

### Ignore specific exports:

Add `// @knip-ignore` comment above the export:

```typescript
// @knip-ignore - Used by external tools
export function utilityFunction() {}
```

## Troubleshooting

### Common Issues

1. **False positives for Next.js files**

   - Make sure entry patterns match your file structure
   - Check that Next.js conventions are properly configured

2. **Dependencies marked as unused but are used**

   - Check if they're used in configuration files
   - Add them to `ignoreDependencies` if needed

3. **Types marked as unused**
   - TypeScript types might be used only in type annotations
   - Consider if they're part of your public API

### Getting Help

- Check the [knip documentation](https://knip.dev/)
- Review the configuration file comments
- Run `pnpm knip --help` for command options

## Next Steps

1. Review the current unused items and decide what to keep/remove
2. Clean up obvious unused dependencies
3. Set up regular knip checks in your development workflow
4. Consider adding knip to your CI pipeline

Remember: knip is a tool to help you, not a strict rule. Use your judgment about what to keep or remove based on your project's needs.
