# 收益率对比图表功能

## 概述

新增的收益率对比图表功能使用recharts库，能够直观地展示投资策略收益率、基金收益率和指数收益率的对比分析，帮助用户更好地评估投资策略的表现。

## 功能特性

### 📊 三线对比图表

1. **策略收益率**（蓝色实线）

   - 显示使用投资策略的累计收益率
   - 计算公式：(当前价值 - 累计投入) / 累计投入 × 100%

2. **基金收益率**（绿色实线）

   - 显示基金净值的累计收益率（买入并持有）
   - 计算公式：(当前净值 - 起始净值) / 起始净值 × 100%

3. **指数收益率**（紫色虚线）
   - 显示基准指数的累计收益率
   - 计算公式：(当前指数值 - 起始指数值) / 起始指数值 × 100%

### 📈 关键指标展示

- **最终收益率对比**：显示三种投资方式的最终收益率
- **超额收益分析**：
  - 相对基金超额收益 = 策略收益率 - 基金收益率
  - 相对指数超额收益 = 策略收益率 - 指数收益率

### 🎨 交互式图表

- **悬停提示**：鼠标悬停显示具体日期和收益率数值
- **响应式设计**：自适应不同屏幕尺寸
- **零轴参考线**：清晰显示盈亏分界线
- **图例说明**：详细解释各条线的含义

## 技术实现

### 数据结构

```typescript
// 收益率对比数据
performanceComparison: {
  date: string;
  strategyReturn: number; // 策略累计收益率 (%)
  fundReturn: number; // 基金累计收益率 (%)
  indexReturn?: number; // 指数累计收益率 (%)
}[]
```

### 核心组件

#### PerformanceComparisonChart.tsx

- 使用recharts的LineChart组件
- 自定义Tooltip和Legend
- 响应式容器设计
- 颜色主题统一

#### 回测引擎集成

- 在BacktestEngine中添加generatePerformanceComparison方法
- 自动计算三种收益率数据
- 支持指数数据缺失的情况

### 计算逻辑

```typescript
// 策略收益率计算
const strategyReturn =
  entry.totalInvestment > 0
    ? ((entry.value - entry.totalInvestment) / entry.totalInvestment) * 100
    : 0;

// 基金收益率计算
const fundReturn = ((entry.netAssetValue - startNav) / startNav) * 100;

// 指数收益率计算
const indexReturn =
  startIndexValue > 0
    ? ((currentIndexValue - startIndexValue) / startIndexValue) * 100
    : 0;
```

## 使用方法

### 1. 在回测分析中查看

1. 选择基金和投资策略
2. 设置策略参数
3. 点击"开始回测"
4. 在结果页面查看"收益率对比分析"图表

### 2. 在网格交易演示中查看

1. 点击"网格交易"标签页
2. 调整网格交易参数
3. 点击"开始网格交易回测"
4. 查看收益率对比图表

## 图表解读

### 收益率曲线分析

1. **策略优于基金**：蓝线在绿线上方

   - 说明投资策略表现优于简单买入持有
   - 超额收益为正值

2. **策略劣于基金**：蓝线在绿线下方

   - 说明投资策略表现不如简单买入持有
   - 需要调整策略参数或选择其他策略

3. **策略优于指数**：蓝线在紫色虚线上方
   - 说明投资策略跑赢了市场基准
   - 具有较好的主动管理价值

### 超额收益指标

- **正超额收益**：绿色背景，表示策略表现优秀
- **负超额收益**：红色背景，表示策略需要优化

## 应用场景

### 1. 策略效果评估

- 比较不同投资策略的表现
- 评估策略的有效性
- 优化策略参数

### 2. 基金选择

- 对比不同基金的表现
- 评估基金相对指数的表现
- 选择合适的投资标的

### 3. 风险收益分析

- 观察收益率波动情况
- 分析不同市场环境下的表现
- 评估投资组合的稳定性

## 优化建议

### 1. 数据完整性

- 确保基金和指数数据的时间对齐
- 处理数据缺失的情况
- 提供数据质量指标

### 2. 图表增强

- 添加更多技术指标线
- 支持不同时间周期切换
- 增加图表导出功能

### 3. 分析工具

- 添加相关性分析
- 提供风险调整收益指标
- 增加回撤分析图表

## 注意事项

1. **数据准确性**：确保基金和指数数据的准确性和及时性
2. **时间对齐**：基金和指数数据需要时间对齐，避免比较偏差
3. **基准选择**：选择合适的基准指数进行比较
4. **费用考虑**：实际投资需要考虑交易费用和管理费用
5. **市场环境**：不同市场环境下策略表现可能差异较大

## 扩展功能

### 计划中的功能

- [ ] 多策略同时对比
- [ ] 分段收益率分析
- [ ] 风险调整收益指标
- [ ] 回撤对比分析
- [ ] 收益率分布图
- [ ] 滚动收益率分析

### 技术改进

- [ ] 图表性能优化
- [ ] 数据缓存机制
- [ ] 实时数据更新
- [ ] 移动端适配优化
- [ ] 图表主题定制

## 总结

收益率对比图表功能为基金投资策略回测系统提供了强大的可视化分析工具。通过直观的图表展示，用户可以：

- 清晰地看到策略相对于基金和指数的表现
- 快速识别策略的优势和不足
- 做出更明智的投资决策

该功能结合了现代化的图表库和科学的计算方法，为投资者提供了专业级的分析工具。
