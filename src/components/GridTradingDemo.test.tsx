import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, it, expect, vi, beforeEach } from "vitest";

import GridTradingDemo from "@/components/GridTradingDemo";
import { fetchFundData } from "@/lib/api/fundApi";
import { fetchIndexData } from "@/lib/api/indexApi";
import { BacktestEngine } from "@/lib/backtest";

// Mock dependencies
vi.mock("@/lib/backtest");
vi.mock("@/lib/api/fundApi");
vi.mock("@/lib/api/indexApi");

const mockFundData = [
  { date: "2023-01-01", netAssetValue: 1, accumulatedValue: 1 },
  { date: "2023-06-01", netAssetValue: 1.5, accumulatedValue: 1.5 },
  { date: "2024-01-01", netAssetValue: 2, accumulatedValue: 2 },
];

const mockBacktestResult = {
  strategy: "grid_trading" as const,
  fund: {
    id: "000001",
    name: "测试基金",
    code: "000001",
    type: "股票型",
    riskLevel: "高风险",
  },
  params: {
    startDate: "2023-01-01",
    endDate: "2024-01-01",
    initialAmount: 50_000,
    gridCount: 10,
    priceRange: { min: 1, max: 3 },
    investmentPerGrid: 2000,
    rebalanceFrequency: "weekly" as const,
    stopLoss: 20,
    takeProfit: 50,
  },
  performance: {
    totalInvestment: 50_000,
    finalValue: 65_000,
    totalReturn: 30,
    annualizedReturn: 25.5,
    maxDrawdown: -8.2,
    volatility: 18.5,
    sharpeRatio: 1.38,
  },
  timeline: [
    {
      date: "2023-01-01",
      investment: 50_000,
      shares: 50_000,
      value: 50_000,
      totalInvestment: 50_000,
      netAssetValue: 1,
    },
  ],
};

describe("GridTradingDemo", () => {
  const mockRunBacktest = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock BacktestEngine
    const mockEngine = {
      runBacktest: mockRunBacktest,
    };
    (BacktestEngine as any).mockImplementation(() => mockEngine);

    // Mock data functions
    (fetchFundData as any).mockResolvedValue(mockFundData);
    (fetchIndexData as any).mockResolvedValue([]);

    mockRunBacktest.mockResolvedValue(mockBacktestResult);
  });

  it("应该渲染网格交易演示组件", () => {
    render(<GridTradingDemo />);

    expect(screen.getByText("网格交易策略演示")).toBeInTheDocument();
    expect(screen.getByText("开始网格交易回测")).toBeInTheDocument();
  });

  it("应该显示默认参数", () => {
    render(<GridTradingDemo />);

    expect(screen.getByDisplayValue("2023-01-01")).toBeInTheDocument();
    expect(screen.getByDisplayValue("2024-01-01")).toBeInTheDocument();
    expect(screen.getByDisplayValue("50000")).toBeInTheDocument();
    expect(screen.getByDisplayValue("10")).toBeInTheDocument();
    expect(screen.getByDisplayValue("1")).toBeInTheDocument(); // 最低价格
    expect(screen.getByDisplayValue("3")).toBeInTheDocument(); // 最高价格
    expect(screen.getByDisplayValue("2000")).toBeInTheDocument();
  });

  it("应该允许修改参数", async () => {
    const user = userEvent.setup();
    render(<GridTradingDemo />);

    // 修改初始金额
    const initialAmountInput = screen.getByDisplayValue("50000");
    await user.clear(initialAmountInput);
    await user.type(initialAmountInput, "100000");
    expect(screen.getByDisplayValue("100000")).toBeInTheDocument();

    // 修改网格数量
    const gridCountInput = screen.getByDisplayValue("10");
    await user.clear(gridCountInput);
    await user.type(gridCountInput, "20");
    expect(screen.getByDisplayValue("20")).toBeInTheDocument();

    // 修改价格范围
    const minPriceInput = screen.getByDisplayValue("1");
    await user.clear(minPriceInput);
    await user.type(minPriceInput, "0.5");
    expect(screen.getByDisplayValue("0.5")).toBeInTheDocument();
  });

  it("应该执行网格交易回测", async () => {
    const user = userEvent.setup();
    render(<GridTradingDemo />);

    const backtestButton = screen.getByText("开始网格交易回测");
    await user.click(backtestButton);

    await waitFor(() => {
      expect(fetchFundData).toHaveBeenCalled();
      expect(mockRunBacktest).toHaveBeenCalled();
    });

    // 验证回测结果显示
    await waitFor(() => {
      expect(screen.getByText("网格交易回测结果")).toBeInTheDocument();
      expect(screen.getByText("30.00%")).toBeInTheDocument(); // 总收益率
      expect(screen.getByText("25.50%")).toBeInTheDocument(); // 年化收益率
    });
  });

  it("应该显示加载状态", async () => {
    const user = userEvent.setup();
    // 让回测函数延迟返回
    mockRunBacktest.mockImplementation(
      () => new Promise((resolve) => setTimeout(resolve, 100))
    );

    render(<GridTradingDemo />);

    const backtestButton = screen.getByText("开始网格交易回测");
    await user.click(backtestButton);

    // 应该显示加载状态
    expect(screen.getByText("回测中...")).toBeInTheDocument();
    expect(backtestButton).toBeDisabled();
  });

  it("应该处理回测错误", async () => {
    const user = userEvent.setup();
    mockRunBacktest.mockRejectedValue(new Error("回测失败"));

    render(<GridTradingDemo />);

    const backtestButton = screen.getByText("开始网格交易回测");
    await user.click(backtestButton);

    await waitFor(() => {
      expect(screen.getByText("回测失败")).toBeInTheDocument();
    });
  });

  it("应该处理指数数据获取失败", async () => {
    const user = userEvent.setup();
    (fetchIndexData as any).mockRejectedValue(new Error("指数数据获取失败"));

    render(<GridTradingDemo />);

    const backtestButton = screen.getByText("开始网格交易回测");
    await user.click(backtestButton);

    // 即使指数数据获取失败，回测也应该继续
    await waitFor(() => {
      expect(mockRunBacktest).toHaveBeenCalled();
    });
  });

  it("应该显示参数说明", () => {
    render(<GridTradingDemo />);

    expect(screen.getByText("网格交易参数设置")).toBeInTheDocument();
    expect(screen.getByText("初始投资金额")).toBeInTheDocument();
    expect(screen.getByText("网格数量")).toBeInTheDocument();
    expect(screen.getByText("价格区间")).toBeInTheDocument();
    expect(screen.getByText("每格投资金额")).toBeInTheDocument();
    expect(screen.getByText("调仓频率")).toBeInTheDocument();
  });

  it("应该支持调仓频率选择", async () => {
    const user = userEvent.setup();
    render(<GridTradingDemo />);

    // 查找调仓频率选择器
    const frequencySelect = screen.getByDisplayValue("weekly");
    expect(frequencySelect).toBeInTheDocument();

    // 修改调仓频率
    await user.selectOptions(frequencySelect, "daily");
    expect(screen.getByDisplayValue("daily")).toBeInTheDocument();
  });

  it("应该处理可选参数", async () => {
    const user = userEvent.setup();
    render(<GridTradingDemo />);

    // 清空止损比例（可选参数）
    const stopLossInput = screen.getByDisplayValue("20");
    await user.clear(stopLossInput);

    // 清空止盈比例（可选参数）
    const takeProfitInput = screen.getByDisplayValue("50");
    await user.clear(takeProfitInput);

    const backtestButton = screen.getByText("开始网格交易回测");
    await user.click(backtestButton);

    // 应该仍然能够执行回测
    await waitFor(() => {
      expect(mockRunBacktest).toHaveBeenCalled();
    });
  });

  it("应该清除之前的结果和错误", async () => {
    const user = userEvent.setup();

    // 首先触发一个错误
    mockRunBacktest.mockRejectedValue(new Error("测试错误"));
    render(<GridTradingDemo />);

    let backtestButton = screen.getByText("开始网格交易回测");
    await user.click(backtestButton);

    await waitFor(() => {
      expect(screen.getByText("测试错误")).toBeInTheDocument();
    });

    // 然后成功执行回测
    mockRunBacktest.mockResolvedValue(mockBacktestResult);
    backtestButton = screen.getByText("开始网格交易回测");
    await user.click(backtestButton);

    await waitFor(() => {
      // 错误信息应该被清除
      expect(screen.queryByText("测试错误")).not.toBeInTheDocument();
      // 应该显示新的结果
      expect(screen.getByText("网格交易回测结果")).toBeInTheDocument();
    });
  });

  it("应该显示性能指标", async () => {
    const user = userEvent.setup();
    render(<GridTradingDemo />);

    const backtestButton = screen.getByText("开始网格交易回测");
    await user.click(backtestButton);

    await waitFor(() => {
      expect(screen.getByText("总收益率")).toBeInTheDocument();
      expect(screen.getByText("年化收益率")).toBeInTheDocument();
      expect(screen.getByText("最大回撤")).toBeInTheDocument();
      expect(screen.getByText("波动率")).toBeInTheDocument();
      expect(screen.getByText("夏普比率")).toBeInTheDocument();
    });
  });

  it("应该显示收益率对比图表", async () => {
    const user = userEvent.setup();
    render(<GridTradingDemo />);

    const backtestButton = screen.getByText("开始网格交易回测");
    await user.click(backtestButton);

    await waitFor(() => {
      // 验证图表组件存在（通过查找图表相关的文本或元素）
      expect(screen.getByText("收益率对比")).toBeInTheDocument();
    });
  });
});
