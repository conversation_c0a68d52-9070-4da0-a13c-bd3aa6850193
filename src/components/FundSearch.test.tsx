import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, it, expect, vi, beforeEach } from "vitest";

import type { Fund } from "@/types/fund";

import FundSearch from "./FundSearch";

// Mock API calls
vi.mock("@/lib/api/fundApi", () => ({
  FundApiService: vi.fn().mockImplementation(() => ({
    searchFunds: vi.fn(),
    validateFundCode: vi.fn(),
  })),
}));

const mockFunds: Fund[] = [
  {
    id: "000001",
    name: "华夏成长混合",
    code: "000001",
    type: "hybrid",
    riskLevel: "medium",
    indexId: "hs300",
  },
  {
    id: "000628",
    name: "大成高新技术产业股票",
    code: "000628",
    type: "stock",
    riskLevel: "high",
    indexId: "zz500",
  },
];

describe("FundSearch", () => {
  const mockOnFundSelect = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("应该渲染搜索输入框", () => {
    render(
      <FundSearch
        onFundSelect={mockOnFundSelect}
        placeholder="搜索基金代码或名称"
      />
    );

    expect(
      screen.getByPlaceholderText("搜索基金代码或名称")
    ).toBeInTheDocument();
  });

  it("应该在输入时显示搜索结果", async () => {
    const user = userEvent.setup();

    render(<FundSearch onFundSelect={mockOnFundSelect} funds={mockFunds} />);

    const searchInput = screen.getByRole("textbox");
    await user.type(searchInput, "华夏");

    await waitFor(() => {
      expect(screen.getByText("华夏成长混合")).toBeInTheDocument();
    });
  });

  it("应该支持基金代码搜索", async () => {
    const user = userEvent.setup();

    render(<FundSearch onFundSelect={mockOnFundSelect} funds={mockFunds} />);

    const searchInput = screen.getByRole("textbox");
    await user.type(searchInput, "000628");

    await waitFor(() => {
      expect(screen.getByText("大成高新技术产业股票")).toBeInTheDocument();
    });
  });

  it("应该在选择基金时调用回调函数", async () => {
    const user = userEvent.setup();

    render(<FundSearch onFundSelect={mockOnFundSelect} funds={mockFunds} />);

    const searchInput = screen.getByRole("textbox");
    await user.type(searchInput, "华夏");

    await waitFor(() => {
      const fundOption = screen.getByText("华夏成长混合");
      fireEvent.click(fundOption);
    });

    expect(mockOnFundSelect).toHaveBeenCalledWith(mockFunds[0]);
  });

  it("应该显示基金类型和风险等级", async () => {
    const user = userEvent.setup();

    render(<FundSearch onFundSelect={mockOnFundSelect} funds={mockFunds} />);

    const searchInput = screen.getByRole("textbox");
    await user.type(searchInput, "华夏");

    await waitFor(() => {
      expect(screen.getByText("混合型")).toBeInTheDocument();
      expect(screen.getByText("中风险")).toBeInTheDocument();
    });
  });

  it("应该处理无搜索结果", async () => {
    const user = userEvent.setup();

    render(<FundSearch onFundSelect={mockOnFundSelect} funds={mockFunds} />);

    const searchInput = screen.getByRole("textbox");
    await user.type(searchInput, "不存在的基金");

    await waitFor(() => {
      expect(screen.getByText("未找到匹配的基金")).toBeInTheDocument();
    });
  });

  it("应该支持键盘导航", async () => {
    const user = userEvent.setup();

    render(<FundSearch onFundSelect={mockOnFundSelect} funds={mockFunds} />);

    const searchInput = screen.getByRole("textbox");
    await user.type(searchInput, "00");

    await waitFor(() => {
      expect(screen.getByText("华夏成长混合")).toBeInTheDocument();
    });

    await user.keyboard("{ArrowDown}");
    await user.keyboard("{Enter}");

    expect(mockOnFundSelect).toHaveBeenCalledWith(mockFunds[0]);
  });

  it("应该显示加载状态", async () => {
    const user = userEvent.setup();

    render(<FundSearch onFundSelect={mockOnFundSelect} isLoading={true} />);

    const searchInput = screen.getByRole("textbox");
    await user.type(searchInput, "华夏");

    expect(screen.getByText("搜索中...")).toBeInTheDocument();
  });

  it("应该支持清空搜索", async () => {
    const user = userEvent.setup();

    render(<FundSearch onFundSelect={mockOnFundSelect} funds={mockFunds} />);

    const searchInput = screen.getByRole("textbox");
    await user.type(searchInput, "华夏");

    const clearButton = screen.getByRole("button", { name: /清空/ });
    await user.click(clearButton);

    expect(searchInput).toHaveValue("");
  });

  it("应该显示最近搜索历史", () => {
    const recentSearches = ["华夏成长", "大成高新"];

    render(
      <FundSearch
        onFundSelect={mockOnFundSelect}
        recentSearches={recentSearches}
      />
    );

    expect(screen.getByText("最近搜索")).toBeInTheDocument();
    expect(screen.getByText("华夏成长")).toBeInTheDocument();
    expect(screen.getByText("大成高新")).toBeInTheDocument();
  });

  it("应该支持高级筛选", () => {
    render(
      <FundSearch
        onFundSelect={mockOnFundSelect}
        funds={mockFunds}
        showAdvancedFilter={true}
      />
    );

    expect(screen.getByText("高级筛选")).toBeInTheDocument();
    expect(screen.getByLabelText("基金类型")).toBeInTheDocument();
    expect(screen.getByLabelText("风险等级")).toBeInTheDocument();
  });

  it("应该处理API错误", async () => {
    const user = userEvent.setup();

    render(<FundSearch onFundSelect={mockOnFundSelect} error="网络连接失败" />);

    expect(screen.getByText("网络连接失败")).toBeInTheDocument();
  });

  it("应该支持批量选择", async () => {
    const user = userEvent.setup();

    render(
      <FundSearch
        onFundSelect={mockOnFundSelect}
        funds={mockFunds}
        multiSelect={true}
      />
    );

    const searchInput = screen.getByRole("textbox");
    await user.type(searchInput, "00");

    await waitFor(() => {
      const checkboxes = screen.getAllByRole("checkbox");
      expect(checkboxes).toHaveLength(2);
    });
  });

  it("应该显示基金详细信息", async () => {
    const user = userEvent.setup();

    render(
      <FundSearch
        onFundSelect={mockOnFundSelect}
        funds={mockFunds}
        showDetails={true}
      />
    );

    const searchInput = screen.getByRole("textbox");
    await user.type(searchInput, "华夏");

    await waitFor(() => {
      const infoButton = screen.getByRole("button", { name: /详情/ });
      fireEvent.click(infoButton);
    });

    expect(screen.getByText("基金详情")).toBeInTheDocument();
  });

  it("应该支持收藏功能", async () => {
    const user = userEvent.setup();

    render(
      <FundSearch
        onFundSelect={mockOnFundSelect}
        funds={mockFunds}
        showFavorites={true}
      />
    );

    const searchInput = screen.getByRole("textbox");
    await user.type(searchInput, "华夏");

    await waitFor(() => {
      const favoriteButton = screen.getByRole("button", { name: /收藏/ });
      expect(favoriteButton).toBeInTheDocument();
    });
  });

  it("应该处理选中状态", () => {
    render(
      <FundSearch
        onFundSelect={mockOnFundSelect}
        funds={mockFunds}
        selectedFund={mockFunds[0]}
      />
    );

    expect(
      screen.getByDisplayValue("华夏成长混合 (000001)")
    ).toBeInTheDocument();
  });
});
