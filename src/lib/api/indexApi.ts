// 指数数据API服务

import type { IndexData } from "@/types/fund";

import type { CSIndexApiResponse, DataFetchOptions } from "./types";
import {
  parseCSIndexData,
  buildCSIndexApiUrl,
  formatDateForCSIndex,
  getCSIndexName,
  createApiError,
  getCacheItem,
  setCacheItem,
  withRetry,
  withTimeout,
  generateCacheKey,
  validateDateRange,
  formatErrorMessage,
} from "./utils";

/**
 * 指数API服务函数
 */

// 默认配置
const defaultOptions: Required<DataFetchOptions> = {
  useCache: true,
  timeout: 10_000,
  retryCount: 3,
  retryDelay: 1000,
};

/**
 * 验证指数代码是否有效
 */
function validateIndexCode(indexCode: string): boolean {
  // 指数代码通常是6位数字
  return /^\d{6}$/.test(indexCode);
}

/**
 * 获取指数历史数据
 */
async function getIndexData(
  indexCode: string,
  startDate?: string,
  endDate?: string,
  options: DataFetchOptions = {}
): Promise<IndexData[]> {
  const options_ = { ...defaultOptions, ...options };

  // 验证指数代码
  if (!validateIndexCode(indexCode)) {
    throw createApiError("INVALID_INDEX_CODE", `无效的指数代码: ${indexCode}`);
  }

  // 验证日期范围
  if (startDate && endDate && !validateDateRange(startDate, endDate)) {
    throw createApiError("INVALID_DATE_RANGE", "开始日期不能晚于结束日期");
  }

  // 检查缓存
  const cacheKey = generateCacheKey(indexCode, startDate, endDate);
  if (options_.useCache) {
    const cachedData = getCacheItem<IndexData[]>(cacheKey);
    if (cachedData) {
      return cachedData;
    }
  }

  try {
    // 计算日期范围
    const { formattedStartDate, formattedEndDate } = calculateDateRange(
      startDate,
      endDate
    );

    // 获取原始数据
    let indexData = await fetchRawIndexData(
      indexCode,
      formattedStartDate,
      formattedEndDate,
      options_
    );

    // 应用日期过滤
    if (startDate || endDate) {
      indexData = filterByDateRange(indexData, startDate, endDate);
    }

    // 缓存结果
    if (options_.useCache) {
      setCacheItem(cacheKey, indexData);
    }

    return indexData;
  } catch (error) {
    const errorMessage = formatErrorMessage(error);
    throw createApiError(
      "API_ERROR",
      `获取指数数据失败: ${errorMessage}`,
      error
    );
  }
}

/**
 * 获取指数基本信息
 */
async function getIndexInfo(
  indexCode: string
): Promise<{ code: string; name: string } | null> {
  if (!validateIndexCode(indexCode)) {
    return null;
  }

  const name = getCSIndexName(indexCode);
  return { code: indexCode, name };
}

/**
 * 获取原始指数API数据
 */
async function fetchRawIndexData(
  indexCode: string,
  startDate: string,
  endDate: string,
  options: Required<DataFetchOptions>
): Promise<IndexData[]> {
  // 在浏览器环境中，使用Next.js API路由避免CORS问题
  if (globalThis.window !== undefined) {
    return fetchViaApiRoute(indexCode, startDate, endDate, options);
  }

  // 在服务端环境中，直接调用外部API
  const url = buildCSIndexApiUrl(indexCode, startDate, endDate);

  const fetchWithRetry = () =>
    withRetry(
      async () => {
        const response = await fetch(url, {
          method: "GET",
          headers: {
            "User-Agent":
              "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
            Accept: "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Cache-Control": "no-cache",
            Pragma: "no-cache",
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.code !== "200") {
          throw new Error(`API错误: ${data.msg}`);
        }

        return data as CSIndexApiResponse;
      },
      { maxRetries: options.retryCount, delay: options.retryDelay }
    );

  const rawData = await withTimeout(fetchWithRetry(), options.timeout);
  const parsedData = parseCSIndexData(rawData);

  // 转换为项目内部格式
  return parsedData.map((item) => ({
    date: item.time,
    value: item.close,
    change: item.range || 0,
    changePercent: item.ratio || 0,
  }));
}

/**
 * 通过Next.js API路由获取数据（避免CORS问题）
 */
async function fetchViaApiRoute(
  indexCode: string,
  startDate: string,
  endDate: string,
  options?: Required<DataFetchOptions>
): Promise<IndexData[]> {
  const parameters = new URLSearchParams({
    code: indexCode,
    startDate,
    endDate,
  });

  const fetchWithRetry = () =>
    withRetry(
      async () => {
        const response = await fetch(`/api/index?${parameters.toString()}`, {
          method: "GET",
          headers: {
            Accept: "application/json, text/plain, */*",
            "Cache-Control": "no-cache",
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        if (!result.success) {
          throw new Error(`API错误: ${result.error}`);
        }

        return result.data as IndexData[];
      },
      {
        maxRetries: options?.retryCount || 3,
        delay: options?.retryDelay || 1000,
      }
    );

  return withTimeout(fetchWithRetry(), options?.timeout || 10_000);
}

/**
 * 按日期范围过滤数据
 */
function filterByDateRange(
  data: IndexData[],
  startDate?: string,
  endDate?: string
): IndexData[] {
  return data.filter((item) => {
    const itemDate = new Date(item.date);

    if (startDate && itemDate < new Date(startDate)) {
      return false;
    }

    if (endDate && itemDate > new Date(endDate)) {
      return false;
    }

    return true;
  });
}

/**
 * 计算日期范围
 */
function calculateDateRange(
  startDate?: string,
  endDate?: string
): { formattedStartDate: string; formattedEndDate: string } {
  const now = new Date();
  const oneYearAgo = new Date(
    now.getFullYear() - 1,
    now.getMonth(),
    now.getDate()
  );

  const start = startDate ? new Date(startDate) : oneYearAgo;
  const end = endDate ? new Date(endDate) : now;

  return {
    formattedStartDate: formatDateForCSIndex(start.toISOString().split("T")[0]),
    formattedEndDate: formatDateForCSIndex(end.toISOString().split("T")[0]),
  };
}

// 便捷函数
export async function fetchIndexData(
  indexCode: string,
  startDate?: string,
  endDate?: string,
  options?: DataFetchOptions
): Promise<IndexData[]> {
  return getIndexData(indexCode, startDate, endDate, options);
}

export async function getIndexBasicInfo(indexCode: string) {
  return getIndexInfo(indexCode);
}
