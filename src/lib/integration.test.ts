import { describe, it, expect, vi, beforeEach } from "vitest";

import { fetchFundData } from "@/lib/api/fundApi";
import { fetchIndexData } from "@/lib/api/indexApi";
import { BacktestEngine } from "@/lib/backtest";
import { getStrategy } from "@/lib/strategies";
import type {
  Fund,
  FundData,
  IndexData,
  FixedAmountParams,
  GridTradingParams,
} from "@/types/fund";

// Mock API functions
vi.mock("@/lib/api/fundApi", () => ({
  fetchFundData: vi.fn(),
}));

vi.mock("@/lib/api/indexApi", () => ({
  fetchIndexData: vi.fn(),
}));

describe("回测流程集成测试", () => {
  const mockFund: Fund = {
    id: "fund_001",
    name: "测试基金",
    code: "000001",
    type: "stock",
    indexId: "index_001",
  };

  const mockFundData: FundData[] = [
    { date: "2024-01-01", netAssetValue: 1, accumulatedValue: 1 },
    { date: "2024-02-01", netAssetValue: 1.05, accumulatedValue: 1.05 },
    { date: "2024-03-01", netAssetValue: 0.98, accumulatedValue: 0.98 },
    { date: "2024-04-01", netAssetValue: 1.12, accumulatedValue: 1.12 },
    { date: "2024-05-01", netAssetValue: 1.08, accumulatedValue: 1.08 },
    { date: "2024-06-01", netAssetValue: 1.15, accumulatedValue: 1.15 },
  ];

  const mockIndexData: IndexData[] = [
    { date: "2024-01-01", value: 3000, change: 0 },
    { date: "2024-02-01", value: 3150, change: 5 },
    { date: "2024-03-01", value: 2940, change: -6.67 },
    { date: "2024-04-01", value: 3360, change: 14.29 },
    { date: "2024-05-01", value: 3240, change: -3.57 },
    { date: "2024-06-01", value: 3450, change: 6.48 },
  ];

  beforeEach(() => {
    vi.clearAllMocks();

    const mockFetchFundData = fetchFundData as any;
    const mockFetchIndexData = fetchIndexData as any;

    mockFetchFundData.mockResolvedValue(mockFundData);
    mockFetchIndexData.mockResolvedValue(mockIndexData);
  });

  describe("完整的定投策略回测流程", () => {
    it("应该完成从数据获取到结果生成的完整流程", async () => {
      // 1. 获取策略配置
      const strategy = getStrategy("fixed_amount");
      expect(strategy).toBeDefined();

      // 2. 准备策略参数
      const parameters: FixedAmountParams = {
        startDate: "2024-01-01",
        endDate: "2024-06-01",
        initialAmount: 10_000,
        monthlyAmount: 2000,
        frequency: "monthly",
      };

      // 3. 获取基金数据
      const fundData = await fetchFundData(
        mockFund.code,
        parameters.startDate,
        parameters.endDate
      );
      expect(fundData).toEqual(mockFundData);

      // 4. 获取指数数据
      const indexData = await fetchIndexData(
        mockFund.indexId!,
        parameters.startDate,
        parameters.endDate
      );
      expect(indexData).toEqual(mockIndexData);

      // 5. 创建回测引擎
      const engine = new BacktestEngine(fundData, indexData);

      // 6. 执行回测
      const result = await engine.runBacktest(mockFund, parameters);

      // 7. 验证回测结果
      expect(result).toBeDefined();
      expect(result.strategy).toBe("fixed_amount");
      expect(result.fund).toEqual(mockFund);
      expect(result.params).toEqual(parameters);

      // 验证性能指标
      expect(result.performance).toBeDefined();
      expect(result.performance.totalInvestment).toBeGreaterThan(0);
      expect(result.performance.finalValue).toBeGreaterThan(0);
      expect(typeof result.performance.totalReturn).toBe("number");
      expect(typeof result.performance.annualizedReturn).toBe("number");
      expect(typeof result.performance.maxDrawdown).toBe("number");
      expect(typeof result.performance.volatility).toBe("number");
      expect(typeof result.performance.sharpeRatio).toBe("number");

      // 验证时间线数据
      expect(result.timeline).toBeDefined();
      expect(Array.isArray(result.timeline)).toBe(true);
      expect(result.timeline.length).toBeGreaterThan(0);

      // 验证性能对比数据
      expect(result.performanceComparison).toBeDefined();
      expect(Array.isArray(result.performanceComparison)).toBe(true);
    });

    it("应该正确计算定投的投资时间线", async () => {
      const parameters: FixedAmountParams = {
        startDate: "2024-01-01",
        endDate: "2024-06-01",
        initialAmount: 10_000,
        monthlyAmount: 2000,
        frequency: "monthly",
      };

      const fundData = await fetchFundData(
        mockFund.code,
        parameters.startDate,
        parameters.endDate
      );
      const engine = new BacktestEngine(fundData);
      const result = await engine.runBacktest(mockFund, parameters);

      // 验证投资时间线的逻辑
      expect(result.timeline.length).toBeGreaterThan(0);

      // 第一笔应该是初始投资
      const firstInvestment = result.timeline[0];
      expect(firstInvestment.investment).toBe(10_000);
      expect(firstInvestment.totalInvestment).toBe(10_000);

      // 后续投资应该是月投金额
      if (result.timeline.length > 1) {
        const secondInvestment = result.timeline[1];
        expect(secondInvestment.investment).toBe(2000);
        expect(secondInvestment.totalInvestment).toBe(12_000);
      }

      // 验证累计投资金额递增
      for (let index = 1; index < result.timeline.length; index++) {
        expect(result.timeline[index].totalInvestment).toBeGreaterThanOrEqual(
          result.timeline[index - 1].totalInvestment
        );
      }
    });
  });

  describe("网格交易策略回测流程", () => {
    it("应该完成网格交易策略的完整回测", async () => {
      const parameters: GridTradingParams = {
        startDate: "2024-01-01",
        endDate: "2024-06-01",
        initialAmount: 50_000,
        gridCount: 10,
        priceRange: { min: 0.95, max: 1.2 },
        investmentPerGrid: 5000,
        rebalanceFrequency: "monthly",
      };

      const fundData = await fetchFundData(
        mockFund.code,
        parameters.startDate,
        parameters.endDate
      );
      const indexData = await fetchIndexData(
        mockFund.indexId!,
        parameters.startDate,
        parameters.endDate
      );

      const engine = new BacktestEngine(fundData, indexData);
      const result = await engine.runBacktest(mockFund, parameters);

      expect(result.strategy).toBe("grid_trading");
      expect(result.performance).toBeDefined();
      expect(result.timeline.length).toBeGreaterThan(0);

      // 网格交易特有的验证
      expect(result.params).toEqual(parameters);
      expect(result.performance.totalInvestment).toBeGreaterThan(0);
    });
  });

  describe("错误处理和边界情况", () => {
    it("应该处理数据获取失败", async () => {
      const mockFetchFundData = fetchFundData as any;
      mockFetchFundData.mockRejectedValue(new Error("数据获取失败"));

      await expect(
        fetchFundData(mockFund.code, "2024-01-01", "2024-06-01")
      ).rejects.toThrow("数据获取失败");
    });

    it("应该处理空数据集", async () => {
      const mockFetchFundData = fetchFundData as any;
      mockFetchFundData.mockResolvedValue([]);

      const fundData = await fetchFundData(
        mockFund.code,
        "2024-01-01",
        "2024-06-01"
      );
      const engine = new BacktestEngine(fundData);

      const parameters: FixedAmountParams = {
        startDate: "2024-01-01",
        endDate: "2024-06-01",
        initialAmount: 10_000,
        monthlyAmount: 2000,
        frequency: "monthly",
      };

      const result = await engine.runBacktest(mockFund, parameters);

      expect(result.timeline.length).toBe(0);
      expect(result.performance.totalInvestment).toBe(0);
      expect(result.performance.finalValue).toBe(0);
    });

    it("应该处理无效的日期范围", async () => {
      const parameters: FixedAmountParams = {
        startDate: "2024-12-31", // 超出数据范围
        endDate: "2024-12-31",
        initialAmount: 10_000,
        monthlyAmount: 2000,
        frequency: "monthly",
      };

      const fundData = await fetchFundData(
        mockFund.code,
        "2024-01-01",
        "2024-06-01"
      );
      const engine = new BacktestEngine(fundData);
      const result = await engine.runBacktest(mockFund, parameters);

      expect(result.timeline.length).toBe(0);
    });

    it("应该处理缺失的指数数据", async () => {
      const mockFetchIndexData = fetchIndexData as any;
      mockFetchIndexData.mockRejectedValue(new Error("指数数据不可用"));

      const fundData = await fetchFundData(
        mockFund.code,
        "2024-01-01",
        "2024-06-01"
      );

      // 即使指数数据获取失败，回测仍应能进行
      const engine = new BacktestEngine(fundData);

      const parameters: FixedAmountParams = {
        startDate: "2024-01-01",
        endDate: "2024-06-01",
        initialAmount: 10_000,
        monthlyAmount: 2000,
        frequency: "monthly",
      };

      const result = await engine.runBacktest(mockFund, parameters);

      expect(result).toBeDefined();
      expect(result.timeline.length).toBeGreaterThan(0);
    });
  });

  describe("性能指标计算验证", () => {
    it("应该计算合理的性能指标", async () => {
      const parameters: FixedAmountParams = {
        startDate: "2024-01-01",
        endDate: "2024-06-01",
        initialAmount: 10_000,
        monthlyAmount: 2000,
        frequency: "monthly",
      };

      const fundData = await fetchFundData(
        mockFund.code,
        parameters.startDate,
        parameters.endDate
      );
      const indexData = await fetchIndexData(
        mockFund.indexId!,
        parameters.startDate,
        parameters.endDate
      );

      const engine = new BacktestEngine(fundData, indexData);
      const result = await engine.runBacktest(mockFund, parameters);

      const { performance } = result;

      // 验证指标的合理性
      expect(performance.totalInvestment).toBeGreaterThan(0);
      expect(performance.finalValue).toBeGreaterThan(0);

      // 收益率应该在合理范围内
      expect(performance.totalReturn).toBeGreaterThan(-100); // 不会亏损超过100%
      expect(performance.totalReturn).toBeLessThan(1000); // 不会收益超过1000%

      // 最大回撤应该在0-100%之间
      expect(performance.maxDrawdown).toBeGreaterThanOrEqual(0);
      expect(performance.maxDrawdown).toBeLessThanOrEqual(100);

      // 波动率应该为正数
      expect(performance.volatility).toBeGreaterThanOrEqual(0);

      // 夏普比率应该是有限数值
      expect(Number.isFinite(performance.sharpeRatio)).toBe(true);
    });
  });

  describe("数据一致性验证", () => {
    it("应该确保时间线数据的一致性", async () => {
      const parameters: FixedAmountParams = {
        startDate: "2024-01-01",
        endDate: "2024-06-01",
        initialAmount: 10_000,
        monthlyAmount: 2000,
        frequency: "monthly",
      };

      const fundData = await fetchFundData(
        mockFund.code,
        parameters.startDate,
        parameters.endDate
      );
      const engine = new BacktestEngine(fundData);
      const result = await engine.runBacktest(mockFund, parameters);

      const { timeline } = result;

      if (timeline.length > 0) {
        // 验证日期顺序
        for (let index = 1; index < timeline.length; index++) {
          const previousDate = new Date(timeline[index - 1].date);
          const currentDate = new Date(timeline[index].date);
          expect(currentDate.getTime()).toBeGreaterThanOrEqual(previousDate.getTime());
        }

        // 验证数值的逻辑关系
        for (const point of timeline) {
          expect(point.totalInvestment).toBeGreaterThanOrEqual(0);
          expect(point.shares).toBeGreaterThanOrEqual(0);
          expect(point.value).toBeGreaterThanOrEqual(0);
          expect(point.netAssetValue).toBeGreaterThan(0);

          // 验证价值计算的一致性
          const calculatedValue = point.shares * point.netAssetValue;
          expect(Math.abs(point.value - calculatedValue)).toBeLessThan(0.01);
        }
      }
    });
  });
});
