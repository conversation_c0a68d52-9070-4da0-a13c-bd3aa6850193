// 应用配置文件

export const APP_CONFIG = {
  // 应用基本信息
  name: "基金投资策略回测计算器",
  version: "1.0.0",
  description: "基于 Next.js 和 React 19 构建的基金投资策略回测分析工具",

  // 默认设置
  defaults: {
    // 默认回测时间范围
    startDate: "2020-01-01",
    endDate: "2024-01-01",

    // 默认投资金额
    initialAmount: 10_000,
    monthlyAmount: 1000,

    // 默认策略
    strategy: "fixed_amount",

    // 默认基金
    fund: "fund_001",
  },

  // 数据源配置
  dataSource: {
    // API 配置（用于真实数据接入）
    api: {
      // 百度股市通API配置
      baidu: {
        baseUrl: "https://gushitong.baidu.com/opendata",
        resourceId: "5824",
        timeout: 10_000,
        retryCount: 3,
        retryDelay: 1000,
      },

      // 其他API配置预留
      alternative: {
        baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || "",
        timeout: 10_000,
        retryCount: 3,
      },
    },

    // 数据缓存配置
    cache: {
      enabled: true,
      ttl: 5 * 60 * 1000, // 5分钟
      maxSize: 100, // 最大缓存条目数
    },

    // 数据源优先级
    priority: ["baidu", "alternative", "mock"],
  },

  // 回测配置
  backtest: {
    // 最大回测期间（年）
    maxPeriodYears: 10,

    // 最小投资金额
    minInvestment: 100,

    // 最大投资金额
    maxInvestment: 1_000_000,

    // 支持的投资频率
    frequencies: ["weekly", "monthly", "quarterly"],

    // 风险指标计算配置
    riskMetrics: {
      // 无风险利率（用于夏普比率计算）
      riskFreeRate: 0.03,

      // 年化因子
      annualizationFactor: 252,

      // 置信区间
      confidenceLevel: 0.95,
    },
  },

  // UI 配置
  ui: {
    // 主题配置
    theme: {
      primary: "#3b82f6",
      secondary: "#64748b",
      success: "#10b981",
      warning: "#f59e0b",
      error: "#ef4444",
    },

    // 图表配置
    chart: {
      width: 800,
      height: 400,
      colors: {
        investment: "#94a3b8",
        value: "#3b82f6",
        profit: "#10b981",
        loss: "#ef4444",
      },

      // 动画配置
      animation: {
        duration: 300,
        easing: "ease-in-out",
      },
    },

    // 分页配置
    pagination: {
      defaultPageSize: 10,
      pageSizeOptions: [10, 20, 50, 100],
    },
  },

  // 功能开关
  features: {
    // 是否启用策略对比
    strategyComparison: false,

    // 是否启用报告导出
    reportExport: false,

    // 是否启用用户账户
    userAccounts: false,

    // 是否启用实时数据
    realTimeData: false,

    // 是否启用高级图表
    advancedCharts: false,
  },

  // 验证规则
  validation: {
    // 日期格式
    dateFormat: "YYYY-MM-DD",

    // 金额范围
    amount: {
      min: 100,
      max: 10_000_000,
    },

    // 百分比范围
    percentage: {
      min: 0,
      max: 100,
    },

    // 期间范围（天）
    period: {
      min: 1,
      max: 3650,
    },
  },

  // 错误消息
  errorMessages: {
    network: "网络连接失败，请检查网络设置",
    dataLoad: "数据加载失败，请稍后重试",
    validation: "参数验证失败，请检查输入",
    calculation: "计算过程出错，请重新设置参数",
    unknown: "未知错误，请联系技术支持",
  },

  // 开发配置
  development: {
    // 是否启用调试模式
    debug: process.env.NODE_ENV === "development",

    // 是否显示性能指标
    showPerformance: false,

    // 是否启用热重载
    hotReload: true,

    // 日志级别
    logLevel: "info",
  },
};

// 环境变量配置
export const ENV_CONFIG = {
  isDevelopment: process.env.NODE_ENV === "development",
  isProduction: process.env.NODE_ENV === "production",

  // API 配置
  apiBaseUrl: process.env.NEXT_PUBLIC_API_BASE_URL,
  apiKey: process.env.NEXT_PUBLIC_API_KEY,

  // 数据库配置
  databaseUrl: process.env.DATABASE_URL,

  // 第三方服务配置
  analyticsId: process.env.NEXT_PUBLIC_ANALYTICS_ID,
  sentryDsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
};

// 获取配置值的辅助函数
export function getConfig<T>(path: string, defaultValue?: T): T {
  const keys = path.split(".");
  let value: any = APP_CONFIG;

  for (const key of keys) {
    if (value && typeof value === "object" && key in value) {
      value = value[key];
    } else {
      return defaultValue as T;
    }
  }

  return value as T;
}

// 验证配置的辅助函数
export function validateConfig(): boolean {
  try {
    // 验证必要的配置项
    const requiredConfigs = [
      "name",
      "version",
      "defaults.startDate",
      "defaults.endDate",
    ];

    for (const config of requiredConfigs) {
      const value = getConfig(config);
      if (value === undefined || value === null) {
        console.error(`Missing required config: ${config}`);
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error("Config validation failed:", error);
    return false;
  }
}

// 导出类型定义
export type AppConfig = typeof APP_CONFIG;
export type EnvConfig = typeof ENV_CONFIG;
