// 基金业绩对比API路由

import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";

import type { BaiduApiResponse } from "@/lib/api/types";
import {
  buildApiUrl,
  parsePerformanceData,
  withRetry,
  withTimeout,
} from "@/lib/api/utils";

// 服务端直接调用外部API获取业绩对比数据
async function fetchPerformanceDataFromBaidu(
  fundCode: string,
  months = 12
): Promise<any> {
  const url = buildApiUrl(fundCode, {
    dataType: "ai",
    months,
    source: "qieman",
  });

  const fetchWithRetry = () =>
    withRetry(
      async () => {
        const response = await fetch(url, {
          method: "GET",
          headers: {
            "User-Agent":
              "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
            Accept: "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Cache-Control": "no-cache",
            Pragma: "no-cache",
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.ResultCode !== "0") {
          throw new Error(`API错误: ${data.ResultCode}`);
        }

        return data as BaiduApiResponse;
      },
      { maxRetries: 3, delay: 1000 }
    );

  const rawData = await withTimeout(fetchWithRetry(), 15_000);
  return parsePerformanceData(rawData);
}

// GET /api/fund/performance?code=000628&months=12
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const fundCode = searchParams.get("code");
    const monthsParameter = searchParams.get("months");
    const months = monthsParameter ? Number.parseInt(monthsParameter, 10) : 12;

    if (!fundCode) {
      return NextResponse.json({ error: "基金代码不能为空" }, { status: 400 });
    }

    // 验证基金代码格式
    if (!/^\d{6}$/.test(fundCode)) {
      return NextResponse.json(
        { error: "基金代码格式错误，应为6位数字" },
        { status: 400 }
      );
    }

    // 验证月数参数
    const validMonths = [1, 3, 6, 12, 36, 60, 124];
    if (!validMonths.includes(months)) {
      return NextResponse.json(
        { error: `月数参数无效，支持的值: ${validMonths.join(", ")}` },
        { status: 400 }
      );
    }

    // 服务端获取业绩对比数据
    const performanceData = await fetchPerformanceDataFromBaidu(
      fundCode,
      months
    );

    return NextResponse.json({
      success: true,
      data: performanceData,
      meta: {
        fundCode,
        months,
      },
    });
  } catch (error) {
    console.error("获取基金业绩对比数据失败:", error);

    const errorMessage = error instanceof Error ? error.message : "未知错误";

    return NextResponse.json(
      {
        success: false,
        error: errorMessage,
        code: "PERFORMANCE_DATA_ERROR",
      },
      { status: 500 }
    );
  }
}
